use walkers::{HttpTiles, MapMemory};
use crate::auth::{AuthState, AuthManager};

/// Shared data that all tabs can access and modify
#[derive(serde::Deserialize, serde::Serialize)]
#[serde(default)]
pub struct SharedData {
    pub counter: i32,
    pub text: String,
    pub slider_value: f32,
    pub checkbox_state: bool,
    pub items: Vec<String>,

    // Authentication state
    pub auth_state: AuthState,

    // Map-related shared state
    #[serde(skip)]
    pub tiles: Option<HttpTiles>,
    #[serde(skip)]
    pub map_memory: MapMemory,

    // Authentication manager (not persisted)
    #[serde(skip)]
    pub auth_manager: AuthManager,
}

impl Default for SharedData {
    fn default() -> Self {
        Self {
            counter: 0,
            text: "Shared text across tabs".to_owned(),
            slider_value: 5.0,
            checkbox_state: false,
            items: vec!["Item 1".to_owned(), "Item 2".to_owned()],
            auth_state: AuthState::default(),
            tiles: None,
            map_memory: MapMemory::default(),
            auth_manager: AuthManager::new(),
        }
    }
}

impl SharedData {
    pub fn new() -> Self {
        Self::default()
    }

    /// Update authentication state using the auth manager
    pub fn update_auth_state(&mut self) {
        self.auth_manager.update_auth_state(&mut self.auth_state);
    }

    /// Start native login process
    #[cfg(not(target_arch = "wasm32"))]
    pub fn start_native_login(&mut self) {
        self.auth_manager.start_native_login();
    }

    /// Cancel authentication process
    #[cfg(not(target_arch = "wasm32"))]
    pub fn cancel_authentication(&mut self) {
        self.auth_manager.cancel_authentication(&mut self.auth_state);
    }

    /// Check if login is in progress
    #[cfg(not(target_arch = "wasm32"))]
    pub fn is_login_in_progress(&self) -> bool {
        self.auth_manager.is_login_in_progress()
    }

    /// Prepare authentication manager for validation of stored auth state
    #[cfg(not(target_arch = "wasm32"))]
    pub fn prepare_auth_validation(&mut self, stored_auth_state: AuthState) {
        self.auth_manager.prepare_for_validation(stored_auth_state.clone());
        // Temporarily set to false until validated
        self.auth_state.is_authenticated = false;
    }

    /// Logout user
    pub fn logout(&mut self) {
        #[cfg(target_arch = "wasm32")]
        {
            crate::auth::logout();
        }

        #[cfg(not(target_arch = "wasm32"))]
        {
            // For native, just clear the auth state
            self.auth_state = AuthState::default();
            log::info!("User logged out");
        }
    }
}