pub mod counter;
pub mod text_editor;
pub mod data_list;
pub mod map;

pub use counter::show_counter_view;
pub use text_editor::show_text_editor_view;
pub use data_list::show_data_list_view;
pub use map::show_map_view;
pub use egui::special_emojis;

#[cfg(target_arch = "wasm32")]
use web_sys;

/// Available tabs/views
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, PartialEq, serde::Deserialize, serde::Serialize)]
pub enum Tab {
    Ingest,
    Verify,
    Analyze,
    Link,
}

impl Default for Tab {
    fn default() -> Self {
        Tab::Ingest
    }
}

impl Tab {
    pub fn name(&self) -> &'static str {
        match self {
            Tab::Ingest => format!("{} Ingest", '\u{E620}'),
            Tab::Verify => "Verify",
            Tab::Analyze => "Analyze",
            Tab::Link => "Link",
        }
    }
}