use crate::shared_data::SharedData;

pub fn show_data_list_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.heading("Data List View");
    ui.separator();

    ui.horizontal(|ui| {
        ui.label("Add item:");
        let mut new_item = String::new();
        if ui.text_edit_singleline(&mut new_item).lost_focus()
            && ui.input(|i| i.key_pressed(egui::Key::Enter))
            && !new_item.is_empty() {
            log::info!("Added new item: '{}'", new_item);
            shared_data.items.push(new_item);
        }
    });

    ui.separator();

    ui.label("Items:");
    let mut to_remove = None;
    for (i, item) in shared_data.items.iter().enumerate() {
        ui.horizontal(|ui| {
            ui.label(format!("{}. {}", i + 1, item));
            if ui.small_button("Remove").clicked() {
                log::info!("Removing item: '{}'", item);
                to_remove = Some(i);
            }
        });
    }

    if let Some(index) = to_remove {
        shared_data.items.remove(index);
    }

    ui.separator();

    ui.horizontal(|ui| {
        ui.label("Counter:");
        ui.label(shared_data.counter.to_string());
        ui.label("| Slider:");
        ui.label(format!("{:.1}", shared_data.slider_value));
    });
}