use std::sync::{mpsc, Arc};

#[cfg(target_arch = "wasm32")]
use wasm_bindgen::prelude::*;

#[cfg(target_arch = "wasm32")]
use web_sys::js_sys;

/// Authentication state structure
#[derive(Debug, Clone, serde::Deserialize, serde::Serialize)]
pub struct AuthState {
    pub is_authenticated: bool,
    pub username: Option<String>,
    pub email: Option<String>,
    pub token: Option<String>,
}

impl Default for AuthState {
    fn default() -> Self {
        Self {
            is_authenticated: false,
            username: None,
            email: None,
            token: None,
        }
    }
}

/// Authentication manager for handling both WASM and native authentication
pub struct AuthManager {
    #[cfg(not(target_arch = "wasm32"))]
    pub login_in_progress: bool,

    #[cfg(not(target_arch = "wasm32"))]
    pub auth_receiver: Option<mpsc::Receiver<Result<AuthState, String>>>,

    #[cfg(not(target_arch = "wasm32"))]
    pub needs_auth_validation: bool,

    #[cfg(not(target_arch = "wasm32"))]
    pub stored_auth_state: Option<AuthState>,

    #[cfg(not(target_arch = "wasm32"))]
    pub validation_in_progress: bool,

    #[cfg(not(target_arch = "wasm32"))]
    pub current_authenticator: Option<Arc<crate::auth_native::NativeAuthenticator>>,
}

impl Default for AuthManager {
    fn default() -> Self {
        Self {
            #[cfg(not(target_arch = "wasm32"))]
            login_in_progress: false,
            #[cfg(not(target_arch = "wasm32"))]
            auth_receiver: None,
            #[cfg(not(target_arch = "wasm32"))]
            needs_auth_validation: false,
            #[cfg(not(target_arch = "wasm32"))]
            stored_auth_state: None,
            #[cfg(not(target_arch = "wasm32"))]
            validation_in_progress: false,
            #[cfg(not(target_arch = "wasm32"))]
            current_authenticator: None,
        }
    }
}

impl AuthManager {
    pub fn new() -> Self {
        Self::default()
    }

    /// Update authentication state
    pub fn update_auth_state(&mut self, auth_state: &mut AuthState) {
        #[cfg(target_arch = "wasm32")]
        {
            *auth_state = get_auth_state();
        }

        #[cfg(not(target_arch = "wasm32"))]
        {
            // Check for authentication results from the channel
            if let Some(ref receiver) = self.auth_receiver {
                if let Ok(result) = receiver.try_recv() {
                    // Handle both login and validation results
                    if self.validation_in_progress {
                        self.validation_in_progress = false;
                        self.needs_auth_validation = false;
                        match result {
                            Ok(new_auth_state) => {
                                log::info!("Stored authentication state validation successful");
                                *auth_state = new_auth_state;
                            }
                            Err(error) => {
                                log::warn!("Stored authentication state validation failed: {}", error);
                                *auth_state = AuthState::default();
                                self.stored_auth_state = None;
                            }
                        }
                        // Clear authenticator reference after validation
                        self.current_authenticator = None;
                    } else {
                        self.login_in_progress = false;
                        match result {
                            Ok(new_auth_state) => {
                                log::info!("Authentication successful");
                                *auth_state = new_auth_state;
                            }
                            Err(error) => {
                                log::error!("Authentication failed: {}", error);
                                *auth_state = AuthState::default();
                            }
                        }
                        // Clear authenticator reference after authentication
                        self.current_authenticator = None;
                    }
                }
            }

            // Handle stored authentication state validation
            if self.needs_auth_validation && !self.validation_in_progress {
                self.start_auth_validation();
            }
        }
    }

    #[cfg(not(target_arch = "wasm32"))]
    pub fn start_native_login(&mut self) {
        use crate::auth_native::NativeAuthenticator;

        if self.login_in_progress {
            log::warn!("Login already in progress");
            return;
        }

        self.login_in_progress = true;
        log::info!("Starting native authentication process");

        // Create a channel for communication
        let (sender, receiver) = mpsc::channel();
        self.auth_receiver = Some(receiver);

        // Create authenticator and store reference for cancellation
        match NativeAuthenticator::new() {
            Ok(authenticator) => {
                let authenticator = Arc::new(authenticator);
                self.current_authenticator = Some(authenticator.clone());

                // Spawn authentication in a separate thread
                std::thread::spawn(move || {
                    let rt = tokio::runtime::Runtime::new().unwrap();
                    rt.block_on(async {
                        match authenticator.authenticate().await {
                            Ok(auth_state) => {
                                let _ = sender.send(Ok(auth_state));
                            }
                            Err(e) => {
                                let _ = sender.send(Err(e.to_string()));
                            }
                        }
                    });
                });
            }
            Err(e) => {
                log::error!("Failed to initialize authenticator: {}", e);
                self.login_in_progress = false;
                let _ = sender.send(Err(format!("Failed to initialize authenticator: {}", e)));
            }
        }
    }

    #[cfg(not(target_arch = "wasm32"))]
    fn start_auth_validation(&mut self) {
        use crate::auth_native::NativeAuthenticator;

        if self.validation_in_progress {
            log::warn!("Validation already in progress");
            return;
        }

        let Some(stored_auth_state) = self.stored_auth_state.clone() else {
            log::warn!("No stored auth state to validate");
            self.needs_auth_validation = false;
            return;
        };

        self.validation_in_progress = true;
        log::info!("Starting validation of stored authentication state");

        // Create a channel for validation results
        let (sender, receiver) = mpsc::channel();
        self.auth_receiver = Some(receiver);

        // Spawn validation in a separate thread
        std::thread::spawn(move || {
            let rt = tokio::runtime::Runtime::new().unwrap();
            rt.block_on(async {
                match NativeAuthenticator::new() {
                    Ok(authenticator) => {
                        match authenticator.validate_stored_auth_state(&stored_auth_state).await {
                            Ok(is_valid) => {
                                if is_valid {
                                    log::info!("Stored authentication state is valid");
                                    let _ = sender.send(Ok(stored_auth_state));
                                } else {
                                    log::warn!("Stored authentication state is invalid");
                                    let _ = sender.send(Err("Stored authentication state is invalid".to_string()));
                                }
                            }
                            Err(e) => {
                                log::error!("Failed to validate stored authentication state: {}", e);
                                let _ = sender.send(Err(format!("Validation failed: {}", e)));
                            }
                        }
                    }
                    Err(e) => {
                        log::error!("Failed to initialize authenticator for validation: {}", e);
                        let _ = sender.send(Err(format!("Failed to initialize authenticator: {}", e)));
                    }
                }
            });
        });
    }

    #[cfg(not(target_arch = "wasm32"))]
    pub fn cancel_authentication(&mut self, auth_state: &mut AuthState) {
        log::info!("Cancelling authentication process");

        // Cancel the current authenticator if it exists
        if let Some(ref authenticator) = self.current_authenticator {
            authenticator.cancel();
        }

        // Reset authentication state
        self.login_in_progress = false;
        self.validation_in_progress = false;
        self.needs_auth_validation = false;

        // Clear the receiver channel to stop listening for auth results
        self.auth_receiver = None;

        // Clear any stored auth state that was being validated
        self.stored_auth_state = None;

        // Clear the authenticator reference
        self.current_authenticator = None;

        // Ensure we're in unauthenticated state
        *auth_state = AuthState::default();

        log::info!("Authentication cancelled - ready for new login attempt");
    }

    #[cfg(not(target_arch = "wasm32"))]
    pub fn prepare_for_validation(&mut self, stored_auth_state: AuthState) {
        if stored_auth_state.is_authenticated {
            log::info!("Found stored authentication state, will validate on startup");
            // Store the original auth state for validation
            self.stored_auth_state = Some(stored_auth_state);
            // Mark that we need to validate the stored auth state
            self.needs_auth_validation = true;
        }
    }

    #[cfg(not(target_arch = "wasm32"))]
    pub fn is_login_in_progress(&self) -> bool {
        self.login_in_progress
    }
}

// WASM bindings for authentication
#[cfg(target_arch = "wasm32")]
pub fn get_auth_state() -> AuthState {
    use wasm_bindgen::JsCast;

    let window = web_sys::window().expect("No window");

    // Check if user is authenticated using the exposed JS function
    let is_authenticated_fn = js_sys::Reflect::get(&window, &JsValue::from_str("isAuthenticated"))
        .unwrap_or(JsValue::NULL);

    let is_authenticated = if let Ok(func) = is_authenticated_fn.dyn_into::<js_sys::Function>() {
        func.call0(&window)
            .unwrap_or(JsValue::FALSE)
            .as_bool()
            .unwrap_or(false)
    } else {
        false
    };

    if !is_authenticated {
        return AuthState::default();
    }

    // Get user info using the exposed JS function
    let get_user_info_fn = js_sys::Reflect::get(&window, &JsValue::from_str("getUserInfo"))
        .unwrap_or(JsValue::NULL);

    let user_info = if let Ok(func) = get_user_info_fn.dyn_into::<js_sys::Function>() {
        func.call0(&window).unwrap_or(JsValue::NULL)
    } else {
        JsValue::NULL
    };

    let mut auth_state = AuthState {
        is_authenticated: true,
        username: None,
        email: None,
        token: None,
    };

    // Extract username and email from user info
    if !user_info.is_null() && !user_info.is_undefined() {
        // Try to get preferred_username
        if let Ok(username) = js_sys::Reflect::get(&user_info, &JsValue::from_str("preferred_username")) {
            if let Some(username_str) = username.as_string() {
                auth_state.username = Some(username_str);
            }
        }

        // Try to get email
        if let Ok(email) = js_sys::Reflect::get(&user_info, &JsValue::from_str("email")) {
            if let Some(email_str) = email.as_string() {
                auth_state.email = Some(email_str);
            }
        }
    }

    // Get token using the exposed JS function
    let get_auth_token_fn = js_sys::Reflect::get(&window, &JsValue::from_str("getAuthToken"))
        .unwrap_or(JsValue::NULL);

    if let Ok(func) = get_auth_token_fn.dyn_into::<js_sys::Function>() {
        if let Ok(token) = func.call0(&window) {
            if let Some(token_str) = token.as_string() {
                auth_state.token = Some(token_str);
            }
        }
    }

    auth_state
}

#[cfg(target_arch = "wasm32")]
pub fn logout() {
    let window = web_sys::window().expect("No window");

    // Call the exposed logout function
    let logout_fn = js_sys::Reflect::get(&window, &JsValue::from_str("logoutUser"))
        .unwrap_or(JsValue::NULL);

    if let Ok(func) = logout_fn.dyn_into::<js_sys::Function>() {
        let _ = func.call0(&window);
    }
}